// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {

  // Smooth scrolling for navigation links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Mobile menu toggle
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (mobileToggle && navMenu) {
      mobileToggle.addEventListener('click', () => {
        navMenu.classList.toggle('active');
        mobileToggle.classList.toggle('active');

        // Prevent body scroll when menu is open
        if (navMenu.classList.contains('active')) {
          document.body.style.overflow = 'hidden';
        } else {
          document.body.style.overflow = '';
        }
      });

      // Close mobile menu when clicking on nav links
      const navLinks = document.querySelectorAll('.nav-link');
      navLinks.forEach(link => {
        link.addEventListener('click', () => {
          navMenu.classList.remove('active');
          mobileToggle.classList.remove('active');
          document.body.style.overflow = '';
        });
      });

      // Close mobile menu when clicking outside
      document.addEventListener('click', (e) => {
        if (!navMenu.contains(e.target) && !mobileToggle.contains(e.target)) {
          navMenu.classList.remove('active');
          mobileToggle.classList.remove('active');
          document.body.style.overflow = '';
        }
      });
    }

    // Navbar scroll effect
    window.addEventListener('scroll', () => {
      const navbar = document.getElementById('navbar');
      if (window.scrollY > 100) {
        navbar.classList.add('scrolled');
      } else {
        navbar.classList.remove('scrolled');
      }
    });

    // Active navigation link highlighting
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');

    window.addEventListener('scroll', () => {
      let current = '';
      sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
          current = section.getAttribute('id');
        }
      });

      navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
          link.classList.add('active');
        }
      });
    });



    // Intersection Observer for animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.project-card, .upcoming-card, .contact-card, .stat-item').forEach(el => {
      observer.observe(el);
    });

    // Scroll down arrow functionality (scroll to About section)
    const scrollDownBtn = document.getElementById('scroll-down-btn');
    if (scrollDownBtn) {
      scrollDownBtn.addEventListener('click', function() {
        const aboutSection = document.getElementById('about');
        if (aboutSection) {
          aboutSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      });
    }

    // Improve mobile touch interactions
    function addTouchFeedback() {
      const touchElements = document.querySelectorAll('.btn-primary, .btn-secondary, .nav-link, .contact-link, .social-link');

      touchElements.forEach(element => {
        element.addEventListener('touchstart', function() {
          this.style.transform = 'scale(0.98)';
        }, { passive: true });

        element.addEventListener('touchend', function() {
          this.style.transform = '';
        }, { passive: true });

        element.addEventListener('touchcancel', function() {
          this.style.transform = '';
        }, { passive: true });
      });
    }

    // Initialize touch feedback on mobile devices
    if ('ontouchstart' in window) {
      addTouchFeedback();
    }

    // Optimize animations for mobile performance
    function optimizeForMobile() {
      const isMobile = window.innerWidth <= 768;
      const animatedElements = document.querySelectorAll('.animate-float, .particle');

      if (isMobile) {
        // Reduce animations on mobile for better performance
        animatedElements.forEach(element => {
          element.style.animationDuration = '8s'; // Slower animations
        });
      }
    }

    // Run optimization on load and resize
    optimizeForMobile();
    window.addEventListener('resize', optimizeForMobile);

    // Improve form validation for mobile
    const formInputs = document.querySelectorAll('input, textarea');
    formInputs.forEach(input => {
      input.addEventListener('blur', function() {
        if (this.value.trim() === '' && this.hasAttribute('required')) {
          this.style.borderColor = 'var(--error-color)';
        } else {
          this.style.borderColor = '';
        }
      });

      input.addEventListener('input', function() {
        if (this.style.borderColor === 'var(--error-color)') {
          this.style.borderColor = '';
        }
      });
    });

    // Contact Form Handling
    const contactForm = document.getElementById('contact-form');
    const formStatus = document.getElementById('form-status');
    const submitBtn = document.getElementById('submit-btn');

    if (contactForm) {
      contactForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span>Sending...</span><i class="fas fa-spinner fa-spin"></i>';
        showFormStatus('Sending your message...', 'loading');

        try {
          const formData = new FormData(contactForm);
          const response = await fetch(contactForm.action, {
            method: 'POST',
            body: formData,
            headers: {
              'Accept': 'application/json'
            }
          });

          if (response.ok) {
            showFormStatus('Thank you! Your message has been sent successfully.', 'success');
            contactForm.reset();
          } else {
            throw new Error('Form submission failed');
          }
        } catch (error) {
          showFormStatus('Sorry, there was an error sending your message. Please try again or contact me directly.', 'error');
        } finally {
          // Reset button state
          submitBtn.disabled = false;
          submitBtn.innerHTML = '<span>Send Message</span><i class="fas fa-paper-plane"></i>';
        }
      });
    }

    function showFormStatus(message, type) {
      formStatus.textContent = message;
      formStatus.className = `form-status ${type}`;
      formStatus.style.display = 'block';

      // Auto-hide success/error messages after 5 seconds
      if (type !== 'loading') {
        setTimeout(() => {
          formStatus.style.display = 'none';
        }, 5000);
      }
    }

  });